{#
/**
 * @file
 * Theme override for a 'form' element - specifically for search forms.
 *
 * Available variables
 * - attributes: A list of HTML attributes for the wrapper element.
 * - children: The child elements of the form.
 *
 * @see template_preprocess_form()
 */
#}
{%
  set classes = [
    'search-form',
  ]
%}
<form{{ attributes.addClass(classes).setAttribute('onsubmit', 'return handleSearchSubmit(this);') }}>
  {{ children }}
</form>
