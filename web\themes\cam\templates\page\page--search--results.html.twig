{#
/**
 * @file
 * Theme override for search results page.
 *
 * Available variables:
 * - logged_in: A flag indicating if user is logged in.
 * - root_path: The root path of the current page (e.g., node, admin, user).
 * - node_type: The content type for the current node, if the page is a node.
 * - head_title: List of text elements that make up the head_title variable.
 * - page_top: Initial rendered markup. This should be printed before 'page'.
 * - page: The rendered page markup.
 * - page_bottom: Closing rendered markup. This variable should be printed after
 *   'page'.
 * - db_offline: A flag indicating if the database is offline.
 * - placeholder_token: The token for generating head, css, js and js-bottom
 *   placeholders.
 *
 * @see template_preprocess_html()
 */
#}
{% set sidebar_two = page.sidebar_second %}

{% if page.alert|render %}
  {{ page.alert }}
{% endif %}

<div{{attributes.addClass('layout-container search-results-page',main_menuorsecondary_menu?'with-navigation',secondary_menu?'with-subnav')}}>

  {% include '@cam/header.html.twig' %}

  <div id="main" class="base_page search-results">
    {% if page.highlighted|render %}
      <div id="highlighted" class="highlighted container">{{ page.highlighted }}</div>
    {% endif %}

    <div class="{% if sidebar_two is not empty %}has-sidebar{% else %}no-sidebar{% endif %}">
      <section class="main_content">
        {% apply spaceless %}
          <div class="content-header">
            {{ page.breadcrumb }}

            {{ title_prefix }}

            {% if title %}
              <h1 class="title">{{ title }}</h1>
            {% endif %}

            {{ title_suffix }}
            {{ page.help }}

            {% if tabs %}
              <nav class="tabs" role="tablist" aria-label="{{ 'Tabs'|t }}">
                {{ tabs }}
              </nav>
            {% endif %}

            {{ page.content }}
          </div>
        {% endapply %}
      </section>

      {% if sidebar_two is not empty %}
        <aside class="sidebar_second" role="complementary">
          {{ sidebar_two }}
        </aside>
      {% endif %}
    </div>
  </div>

  {% include '@cam/footer.html.twig' %}

</div>
