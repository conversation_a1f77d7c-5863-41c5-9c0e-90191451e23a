{#
  Header content for navigation and stuff like that
#}
{% set main_menu = page.primary_menu|render %}
{% set secondary_menu = page.secondary_menu|render %}

<header class="header">
  <div class="container desktop-only utility_nav__wrapper">
    <div class="utility_nav">
      <div class="secondary_menu">
        {{ secondary_menu }}
      </div>
      <div class="search_wrapper">
        <form action="/search/results" class="site_search" id="siteSearch" name="site_search" onsubmit="searchCallback('desktop_search_from_'+window.location.href.split('camosun.ca')[1], this.q.value)">
          <label for="siteSearchInput" class="visually-hidden">{{ 'Search'|t }}</label>
          <input id="siteSearchInput" name='q' type="text" placeholder="{{ 'Search for...'|t }}" class="search_input" autocomplete="off">
          <button type="submit" class="search_submit" id="siteSearchSubmit">
            {{ 'Search'|t }}
            <svg viewbox="0 0 16 16" class="search_icon" tabindex="-1" focusable="false" aria-hidden="true">
              <use xlink:href="#search"></use>
            </svg>
          </button>
        </form>
      </div>
    </div>
  </div>
  <div class="container-menu desktop-only main_menu__wrapper">
    <div class="header__logo">
      {{ page.header }}
    </div>
    <div id="mainMenu" class="main_menu{% if main_menu %} with-primary{% endif %} ">
      {{ main_menu }}
    </div>
  </div>
  <div class="mobile_nav__header mobile-only">
    <div class="header__logo">
      {{ page.header }}
    </div>
    <div class="mobile_nav__close">
      <button class="mobile_nav__button" type="button" aria-expanded="false" aria-controls="mobile_navigation">
        <span class="mobile_nav__button_wrapper">
          <span class="mobile_nav__button_inner"></span>
          <span class="mobile_nav__button_text">
            Menu
          </span>
        </span>
      </button>
    </div>
  </div>
</header>
