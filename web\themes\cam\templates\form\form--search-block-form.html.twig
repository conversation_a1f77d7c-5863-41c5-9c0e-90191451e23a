{#
/**
 * @file
 * Theme override for the search block form.
 *
 * Available variables
 * - attributes: A list of HTML attributes for the wrapper element.
 * - children: The child elements of the form.
 *
 * @see template_preprocess_form()
 */
#}
<form{{ attributes.setAttribute('onsubmit', "return searchCallback('camosun_search_from_'+this.window.location.href.split('camosun.ca')[1], this.q.value);") }}>
  {{ children }}
</form>
