/**
 * @file
 * A JavaScript file for the theme.
 *
 * In order for this JavaScript to be loaded on pages, see the instructions in
 * the README.txt next to this file.
 */

// JavaScript should be made compatible with libraries other than jQuery by
// wrapping it with an "anonymous closure". See:
// - https://drupal.org/node/1446420
// - http://www.adequatelygood.com/2010/3/JavaScript-Module-Pattern-In-Depth
(function (Drupal, $) {
  "use strict";

  // To understand behaviors, see https://www.drupal.org/node/2269515
  Drupal.behaviors.cam = {
    attach: function (context, settings) {
      // polyfill 'array.from'
      if (!Array.from) {
        Array.from = (function () {
          var symbolIterator;
          try {
            symbolIterator = Symbol.iterator
              ? Symbol.iterator
              : "Symbol(Symbol.iterator)";
          } catch (e) {
            symbolIterator = "Symbol(Symbol.iterator)";
          }

          var toStr = Object.prototype.toString;
          var isCallable = function (fn) {
            return (
              typeof fn === "function" || toStr.call(fn) === "[object Function]"
            );
          };
          var toInteger = function (value) {
            var number = Number(value);
            if (isNaN(number)) return 0;
            if (number === 0 || !isFinite(number)) return number;
            return (number > 0 ? 1 : -1) * Math.floor(Math.abs(number));
          };
          var maxSafeInteger = Math.pow(2, 53) - 1;
          var toLength = function (value) {
            var len = toInteger(value);
            return Math.min(Math.max(len, 0), maxSafeInteger);
          };

          var setGetItemHandler = function setGetItemHandler(
            isIterator,
            items
          ) {
            var iterator = isIterator && items[symbolIterator]();
            return function getItem(k) {
              return isIterator ? iterator.next() : items[k];
            };
          };

          var getArray = function getArray(
            T,
            A,
            len,
            getItem,
            isIterator,
            mapFn
          ) {
            // 16. Let k be 0.
            var k = 0;

            // 17. Repeat, while k < len… or while iterator is done (also steps a - h)
            while (k < len || isIterator) {
              var item = getItem(k);
              var kValue = isIterator ? item.value : item;

              if (isIterator && item.done) {
                return A;
              } else {
                if (mapFn) {
                  A[k] =
                    typeof T === "undefined"
                      ? mapFn(kValue, k)
                      : mapFn.call(T, kValue, k);
                } else {
                  A[k] = kValue;
                }
              }
              k += 1;
            }

            if (isIterator) {
              throw new TypeError(
                "Array.from: provided arrayLike or iterator has length more then 2 ** 52 - 1"
              );
            } else {
              A.length = len;
            }

            return A;
          };

          // The length property of the from method is 1.
          return function from(arrayLikeOrIterator /*, mapFn, thisArg */) {
            // 1. Let C be the this value.
            var C = this;

            // 2. Let items be ToObject(arrayLikeOrIterator).
            var items = Object(arrayLikeOrIterator);
            var isIterator = isCallable(items[symbolIterator]);

            // 3. ReturnIfAbrupt(items).
            if (arrayLikeOrIterator == null && !isIterator) {
              throw new TypeError(
                "Array.from requires an array-like object or iterator - not null or undefined"
              );
            }

            // 4. If mapfn is undefined, then let mapping be false.
            var mapFn = arguments.length > 1 ? arguments[1] : void undefined;
            var T;
            if (typeof mapFn !== "undefined") {
              // 5. else
              // 5. a If IsCallable(mapfn) is false, throw a TypeError exception.
              if (!isCallable(mapFn)) {
                throw new TypeError(
                  "Array.from: when provided, the second argument must be a function"
                );
              }

              // 5. b. If thisArg was supplied, let T be thisArg; else let T be undefined.
              if (arguments.length > 2) {
                T = arguments[2];
              }
            }

            // 10. Let lenValue be Get(items, "length").
            // 11. Let len be ToLength(lenValue).
            var len = toLength(items.length);

            // 13. If IsConstructor(C) is true, then
            // 13. a. Let A be the result of calling the [[Construct]] internal method
            // of C with an argument list containing the single item len.
            // 14. a. Else, Let A be ArrayCreate(len).
            var A = isCallable(C) ? Object(new C(len)) : new Array(len);

            return getArray(
              T,
              A,
              len,
              setGetItemHandler(isIterator, items),
              isIterator,
              mapFn
            );
          };
        })();
      }

      // polyfill 'forEach'
      if (window.NodeList && !NodeList.prototype.forEach) {
        NodeList.prototype.forEach = Array.prototype.forEach;
      }

      /**
       * site alerts cookie set
       * @return {[type]} [description]
       */
      var alertType = $(".sitewide_alert").data('alert-type');
      var cookieName = "siteWideAlert";
      var currentAlert = "";
      // Convert the first letter to uppercase
      if (alertType) {
        alertType = alertType.charAt(0).toUpperCase() + alertType.slice(1);
        cookieName = "siteWideAlert" + alertType;
      }

      function alerts() {
        var o = this; // iterate over all alerts on page

        $(".sitewide_alert").each(function () {
          // cookieName = 'siteWideAlert';
          currentAlert = $(this);

          // Hide alert if has been closed and cookie set else show alert
          if (getCookie(cookieName) == "closed") {
            closeAlert(currentAlert);
          } else {
            showAlert(currentAlert);
            $("#main").addClass("has-alert");
          }
        });

        $(".close_alert")
          .one()
          .click(function (e) {
            e.stopPropagation();
            e.preventDefault();

            closeAlert(currentAlert);
            $("#main").removeClass("has-alert");
          });
      }

      function getCookie(cname) {
        var name = cname + "=";
        var ca = document.cookie.split(";");

        for (var i = 0; i < ca.length; i++) {
          var c = ca[i];

          while (c.charAt(0) == " ") {
            c = c.substring(1);
          }

          if (c.indexOf(name) != -1) {
            return c.substring(name.length, c.length);
          }
        }

        return "";
      }

      function closeAlert(currentAlert) {
        $(currentAlert).slideUp(300, function () {
          $(this).remove();
        });

        if (getCookie(cookieName) != "closed") {
          setCookie(cookieName, "closed", 1);
        }
      }

      function showAlert(currentAlert) {
        $(currentAlert).css("display", "block");
      }

      function setCookie(cname, cvalue, exdays) {
        var d = new Date();
        d.setTime(d.getTime() + exdays * 24 * 60 * 60 * 1000);

        var expires = "expires=" + d.toUTCString();

        document.cookie = cname + "=" + cvalue + "; " + expires + ";path=/";
      }

      $(document).ready(function () {
        if (
          window.location.href.indexOf("about/news?") > -1 ||
          window.location.href.indexOf("/events?") > -1
        ) {
          $("html, body").animate(
            { scrollTop: $(".page_title").offset().top },
            "slow"
          );
        }
      });

      /*
      END COOKIE ALERT
       */

      /*
      Click events
       */
      // collapsible sidebar listing filters
      $(".form-item.collapsible > span", context)
        .one()
        .click(function (e) {
          let current_group = $(this).closest(".collapsible");

          if (current_group.hasClass("open")) {
            current_group.removeClass("open");
            $(this).attr("aria-expanded", "false");
          } else {
            current_group.addClass("open");
            $(this).attr("aria-expanded", "true");
          }

          e.preventDefault();
        });

      function toggleMenu() {
        const mobileNavButton = $(".mobile_nav__button", context);

        mobileNavButton.off().on("click", function (e) {
          // transition ordering if alert present
          if ($("#main").hasClass("has-alert")) {
            // if menu visible: hide menu, then show alerts
            if (mobileNavButton.add("#mobile_navigation").hasClass("open")) {
              mobileNavButton.add("#mobile_navigation").removeClass("open");
              mobileNavButton.attr("aria-expanded", "false");
              $("body").removeClass("mobile_nav__open");

              $(".sitewide_alert")
                .delay(300)
                .each(function (i) {
                  currentAlert = $(this);
                  currentAlert.slideToggle(300);
                });
            } else {
              $(".sitewide_alert")
                .each(function (i) {
                  currentAlert = $(this);
                  currentAlert.slideToggle(300);
                })
                .promise()
                .done(function () {
                  mobileNavButton
                    .add("#mobile_navigation")
                    .delay(300)
                    .addClass("open");
                  mobileNavButton.attr("aria-expanded", "true");
                  $("body").delay(300).addClass("mobile_nav__open");
                });
            }
          } else {
            mobileNavButton.add("#mobile_navigation").toggleClass("open");

            const open = mobileNavButton.prop("aria-expanded");

            mobileNavButton.attr("aria-expanded", !open);
            $("body").toggleClass("mobile_nav__open");
          }
        });
      } // end toggleMenu()

      // adjust caption height for images
      function imageCaptions() {
        $(".has-caption").each(function () {
          let capHeight = $(".image_caption", this).outerHeight() + 20;

          $(".page_block__image", this).css("padding-bottom", capHeight + "px");
        });
      }

      // process appropriate action if URL has hash
      function processHash() {
        let currentHash = window.location.hash.substr(1);

        // toggle tab if present in URL hash
        if ($(".tab-link").length) {
          var currentTab = $(".tab-link[onclick*=" + currentHash + "]");

          currentTab.click();

          $(window).on("load", function () {
            // scroll active tab into view
            currentTab[0].scrollIntoView(true);
          });
        }
      }

      // adjust tabbed section 'scroll for more'
      function scrollMore() {
        let tabsWrapper = $(".tab-anchors").length ? $(".tab-anchors") : false;

        if (tabsWrapper) {
          if (tabsWrapper[0].scrollWidth > tabsWrapper[0].clientWidth) {
            tabsWrapper.addClass("mobile-tabs");
          } else {
            tabsWrapper.removeClass("mobile-tabs");
          }
        }
      }

      // open collapsible filters if filter active
      function sidebarFilters() {
        let cFilters = $(
          '.form-item.collapsible input[type="checkbox"], .form-item.collapsible input[type="radio"]'
        );

        cFilters.each(function () {
          if ($(this).is(":checked") && $(this).val() !== "All") {
            $(this).closest(".collapsible").addClass("open");
          }
        });
      }

      // trim whitespace from featured listings
      function adjustFeatured() {
        $(".featured-news, .featured-events, .featured-awards").each(
          function () {
            if ($(this).html().trim().length === 0) {
              $(this).html("");
            }
          }
        );
      }

      // wrap tables within main content to apply overflow styling for mobile support
      const wrapTable = (element) => {
        // skip current table if already wrapped
        if (element.parentNode.classList.contains("table-wrapper")) {
          return;
        }

        // if current table has a wrapping element with no class, add table-wrapper class
        if (
          "DIV" === element.parentNode.tagName &&
          !element.parentNode.classList.length
        ) {
          element.parentNode.classList.add("table-wrapper");

          return;
        }

        let tableWrapper = document.createElement("div");
        tableWrapper.classList.add("table-wrapper");

        element.parentNode.insertBefore(tableWrapper, element);
        tableWrapper.appendChild(element);
      };

      Array.prototype.forEach.call(
        context.querySelectorAll(".main_content table"),
        (element) => {
          wrapTable(element);
        }
      );

      // get all lazy load images
      let lazyImages = Array.from(document.querySelectorAll("img.img-lazy"));

      // image lazy loading JS fallback: if browser doesn't support lazy loading, and supports IntersectionObserver, apply src/srcset attributes as data attrs
      if (
        !("loading" in HTMLImageElement.prototype) &&
        "IntersectionObserver" in window &&
        lazyImages
      ) {
        lazyImages.forEach((lazyImage) => {
          lazyImage.dataset.src = lazyImage.src;
          lazyImage.dataset.srcset = lazyImage.srcset;

          lazyImage.src = null;
          lazyImage.srcset = null;
        });

        let lazyImageObserver = new IntersectionObserver(
          (entries, observer) => {
            entries.forEach((entry) => {
              if (entry.isIntersecting) {
                let lazyImage = entry.target;
                lazyImage.src = lazyImage.dataset.src;
                lazyImage.srcset = lazyImage.dataset.srcset;
                lazyImage.classList.remove("lazy");
                lazyImageObserver.unobserve(lazyImage);
              }
            });
          }
        );

        lazyImages.forEach((lazyImage) => {
          lazyImageObserver.observe(lazyImage);
        });
      }

      // remove lazy loading from images loaded within view
      const lazyLoad = () => {
        // if no items found, bail early
        if (!lazyImages || !lazyImages.length) {
          return;
        }

        // loop through lazy load images
        lazyImages.forEach((lazyImage) => {
          if (
            lazyImage.getBoundingClientRect().top <= window.innerHeight &&
            lazyImage.getBoundingClientRect().bottom >= 0 &&
            getComputedStyle(lazyImage).display !== "none"
          ) {
            // remove lazy loading class and loading attribute if already within viewport
            lazyImage.removeAttribute("loading");
            lazyImage.classList.remove("img-lazy");
          }

          // if image decoding not supported, remove
          if (!("decoding" in HTMLImageElement.prototype)) {
            lazyImage.removeAttribute("decoding");
          }
        });
      };
      lazyLoad();

      // remove 'img-lazy' class from fully loaded images
      lazyImages.forEach((lazyImage) => {
        lazyImage.onload = () => {
          lazyImage.classList.remove("img-lazy");
        };
      });

      $(document).ready(function () {
        toggleMenu();
        alerts();
        imageCaptions();

        if (window.location.hash) {
          processHash();
        }

        scrollMore();
        sidebarFilters();
        adjustFeatured();
      });

      let resizeTimer;
      $(window).on("resize", function () {
        clearTimeout(resizeTimer);
        resizeTimer = setTimeout(function () {
          imageCaptions();
          scrollMore();
        }, 100);
      });
    },
  };

  //trim empty spaces from search input in header and search page
  // $(window).on("load", function () {
  //   if (document.location.href.includes("search-results") || window.location.href.includes("search-results")) {
  //     let btnTrim = document.querySelector("#onpointsearch-submit");
  //     let input = document.querySelector("#onpointsearch-query");
  //     btnTrim.addEventListener('click', function() {
  //       let str = input.value.trim();
  //       input.value = str;
  //     });
  //   }
  // });

  // let btnTrimFront = document.querySelector("#siteSearchSubmit");
  // let inputFront = document.querySelector("#siteSearchInput");
  // btnTrimFront.addEventListener('click', function() {
  //   let strFront = inputFront.value.trim();
  //   inputFront.value = strFront;
  // });
  const searchCallback = (gname, query) => {
   gtag("event", "search", { search_term: query });
   return '';
    };

    window.__gcse || (window.__gcse = {});
    window.__gcse.searchCallbacks = {
      web: {
        starting: searchCallback,
      },
    };
    if ((document.location.href.includes("search/results") || window.location.href.includes("search/results")) && 
          document.querySelector("#search-form")) {
      document.querySelector("#search-form").setAttribute("onsubmit", "searchCallback('search_results_edit_'+window.location.href.split('camosun.ca')[1], this.q.value)");
    }


})(Drupal, jQuery);


