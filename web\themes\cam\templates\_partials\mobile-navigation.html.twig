{#
  Mobile navigation
#}
{% set main_menu = page.primary_menu|render %}
{% set secondary_menu = page.secondary_menu|render %}
{% set sidebar_two = page.sidebar_second|render|striptags|trim %}

<div id="mobile_navigation" class="mobile-only mobile_nav__wrapper">
  <div class="mobile_nav__inner">
    <div class="mobile_menu{% if main_menu %} with-primary{% endif %} ">
      {{ main_menu }}
    </div>
    <div class="search_wrapper">
      <form action="/search/results" class="site_search" id="siteSearchMobile" name="site_search" onsubmit="searchCallback('camosun_search_from_'+this.window.location.href.split('camosun.ca')[1], this.q.value)">
        <label for="siteSearchInputMobile" class="visually-hidden">{{ 'Search'|t }}</label>
        <input id="siteSearchInputMobile" name='q' type="text" placeholder="{{ 'Search for...'|t }}" class="search_input" autocomplete="off">
        <button type="submit" class="search_submit">
          {{ 'Search'|t }}
          <svg viewbox="0 0 16 16" class="search_icon" tabindex="-1" focusable="false" aria-hidden="true">
            <use xlink:href="#search"></use>
          </svg>
        </button>
      </form>
    </div>
    <hr class="mobile_nav__divider">
    <div class="mobile_secondary">
      {{ secondary_menu }}
    </div>
    {% if (sidebar_one is not empty) or (sidebar_two is not empty) %}
      {% set limitSidebarField = drupal_field('field_limit_sidebar_navigation_', 'node', node.id) %}
      {% set limitSidebar = limitSidebarField['#items'].0.value %}
      <div class="mobile_tertiary {% if limitSidebar %} limit-sidebar{% endif %}">
        {% if sidebar_two is not empty %}
          {% apply spaceless %}
            {% block sidebar_second %}
              {% if page.sidebar_second|render %}
                {{ page.sidebar_second }}
              {% endif %}
            {% endblock sidebar_second %}
          {% endapply %}
        {% endif %}
      {% endif %}
    </div>
  </div>
</div>
